<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Enhanced gradients for house structure -->
    <linearGradient id="grad-header" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <!-- Professional gradient color scheme -->
    <linearGradient id="grad-roof" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e11d48;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-upper-floor" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-main-floor" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ea580c;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad-foundation" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6d28d9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5b21b6;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="grad-sky" cx="50%" cy="20%" r="80%">
      <stop offset="0%" style="stop-color:#e0f2fe;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#bae6fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7dd3fc;stop-opacity:1" />
    </radialGradient>
    <!-- Professional window gradient -->
    <linearGradient id="grad-window" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e0f2fe;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#0284c7;stop-opacity:0.3" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Centered container for the entire house -->
  <g transform="translate(130, 0)">

    <!-- Sky Background with clouds -->
    <rect x="-130" y="0" width="1600" height="1000" fill="url(#grad-sky)"/>
    
    <!-- Decorative clouds -->
    <ellipse cx="70" cy="120" rx="40" ry="25" fill="white" opacity="0.7"/>
    <ellipse cx="90" cy="110" rx="35" ry="20" fill="white" opacity="0.6"/>
    <ellipse cx="1270" cy="140" rx="50" ry="30" fill="white" opacity="0.7"/>
    <ellipse cx="1290" cy="130" rx="40" ry="25" fill="white" opacity="0.6"/>
    
    <!-- Ground with landscaping -->
    <ellipse cx="670" cy="950" rx="750" ry="50" fill="#22c55e" opacity="0.8"/>
    <rect x="-130" y="900" width="1600" height="100" fill="#16a34a"/>
    
    <!-- Garden elements -->
    <circle cx="20" cy="890" r="25" fill="#84cc16"/>
    <circle cx="1320" cy="890" r="30" fill="#65a30d"/>
    <rect x="15" y="885" width="10" height="30" fill="#92400e"/>
    <rect x="1315" y="885" width="10" height="35" fill="#92400e"/>
    
    <!-- Title with enhanced styling -->
    <rect x="-130" y="0" width="1600" height="70" fill="url(#grad-header)" filter="url(#shadow)"/>
    <text x="670" y="45" text-anchor="middle" fill="white" font-size="26" font-weight="bold" font-family="sans-serif">CloudBot 智能体架构之家</text>
    
    <!-- House Foundation (Technical Foundation) -->
    <g id="foundation">
      <rect x="180" y="820" width="980" height="180" fill="url(#grad-foundation)" rx="12" filter="url(#shadow)"/>
      <text x="200" y="850" fill="white" font-size="19" font-weight="bold" font-family="sans-serif">技术底座层 - 夯实基础技术</text>
      <text x="750" y="850" fill="#e9d5ff" font-size="12" font-family="sans-serif">🎯 核心目标: 模型性能提升: 20%+, 知识覆盖率: 99%</text>

      <rect x="200" y="870" width="220" height="110" fill="white" rx="8" stroke="#7c3aed" stroke-width="2" filter="url(#shadow)"/>
      <text x="310" y="890" text-anchor="middle" fill="#7c3aed" font-size="13" font-weight="bold" font-family="sans-serif">高阶智能体核心</text>
      <text x="310" y="910" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">Agent Fine-tuning</text>
      <text x="310" y="925" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">Agent Memory</text>
      <text x="310" y="940" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">Iterative Planning</text>
      <text x="310" y="955" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">Multi-Agent Routing</text>
      
      <rect x="440" y="870" width="220" height="110" fill="white" rx="8" stroke="#7c3aed" stroke-width="2" filter="url(#shadow)"/>
      <text x="550" y="890" text-anchor="middle" fill="#7c3aed" font-size="13" font-weight="bold" font-family="sans-serif">知识与上下文工程</text>
      <text x="550" y="910" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">Context Engineering</text>
      <text x="550" y="925" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">SOP体系化建设</text>
      <text x="550" y="940" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">知识图谱存储</text>
      <text x="550" y="955" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">抑制幻觉</text>
      
      <rect x="680" y="870" width="220" height="110" fill="white" rx="8" stroke="#7c3aed" stroke-width="2" filter="url(#shadow)"/>
      <text x="790" y="890" text-anchor="middle" fill="#7c3aed" font-size="13" font-weight="bold" font-family="sans-serif">复杂场景推理</text>
      <text x="790" y="910" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">长链条任务评估</text>
      <text x="790" y="925" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">No SOP推理诊断</text>
      <text x="790" y="940" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">CodeAgent探索</text>
      <text x="790" y="955" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">沙盒环境</text>
      
      <rect x="920" y="870" width="220" height="110" fill="white" rx="8" stroke="#7c3aed" stroke-width="2" filter="url(#shadow)"/>
      <text x="1030" y="890" text-anchor="middle" fill="#7c3aed" font-size="13" font-weight="bold" font-family="sans-serif">团队协作生态</text>
      <text x="1030" y="910" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">🤝 外部工具复用率: 80%</text>
      <text x="1030" y="930" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">🤝 知识库贡献度</text>
      <text x="1030" y="950" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">🤝 ChatBI数据融合</text>
    </g>

    <!-- House Main Floor (Agent Architecture) -->
    <g id="main-floor">
      <rect x="220" y="460" width="900" height="360" fill="url(#grad-main-floor)" rx="12" filter="url(#shadow)"/>
      <text x="240" y="490" fill="white" font-size="19" font-weight="bold" font-family="sans-serif">Agent层 - 构建协同大脑</text>
      
      <!-- Top Box: Dispatcher Agent -->
      <rect x="290" y="505" width="760" height="150" fill="white" rx="8" stroke="#ea580c" stroke-width="2" filter="url(#shadow)"/>
      <text x="670" y="525" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">调度路由智能体</text>
      <text x="670" y="540" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">(Dispatcher Agent)</text>
      <rect x="480" y="550" width="150" height="40" fill="#fef3c7" rx="4" stroke="#f59e0b" stroke-width="1"/>
      <text x="555" y="565" text-anchor="middle" fill="#92400e" font-size="9" font-weight="bold" font-family="sans-serif">意图识别</text>
      <text x="555" y="580" text-anchor="middle" fill="#92400e" font-size="8" font-family="sans-serif">澄清/路由</text>
      <rect x="710" y="550" width="150" height="40" fill="#fef3c7" rx="4" stroke="#f59e0b" stroke-width="1"/>
      <text x="785" y="565" text-anchor="middle" fill="#92400e" font-size="9" font-weight="bold" font-family="sans-serif">执行体选择</text>
      <text x="785" y="580" text-anchor="middle" fill="#92400e" font-size="8" font-family="sans-serif">智能调度</text>
      <rect x="300" y="600" width="740" height="45" fill="#ffe4e6" rx="4" stroke="#f43f5e" stroke-width="1"/>
      <text x="670" y="615" text-anchor="middle" fill="#be123c" font-size="10" font-weight="bold" font-family="sans-serif">🎯 任务路由准确率</text>
      <text x="670" y="633" text-anchor="middle" fill="#be123c" font-size="12" font-family="sans-serif">95%+</text>
      
      <!-- Bottom Row -->
      <rect x="290" y="665" width="200" height="150" fill="white" rx="8" stroke="#ea580c" stroke-width="2" filter="url(#shadow)"/>
      <text x="390" y="685" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">交互诊断智能体</text>
      <text x="390" y="700" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">(Interactive Agent)</text>
      <rect x="300" y="710" width="80" height="40" fill="#dcfce7" rx="4" stroke="#22c55e" stroke-width="1"/>
      <text x="340" y="725" text-anchor="middle" fill="#166534" font-size="9" font-weight="bold" font-family="sans-serif">RAG高质量</text>
      <text x="340" y="740" text-anchor="middle" fill="#166534" font-size="8" font-family="sans-serif">知识检索生成</text>
      <rect x="400" y="710" width="80" height="40" fill="#dcfce7" rx="4" stroke="#22c55e" stroke-width="1"/>
      <text x="440" y="725" text-anchor="middle" fill="#166534" font-size="9" font-weight="bold" font-family="sans-serif">上下文管理</text>
      <text x="440" y="740" text-anchor="middle" fill="#166534" font-size="8" font-family="sans-serif">多轮对话</text>
      <rect x="300" y="760" width="180" height="45" fill="#dbeafe" rx="4" stroke="#3b82f6" stroke-width="1"/>
      <text x="390" y="780" text-anchor="middle" fill="#1e40af" font-size="10" font-weight="bold" font-family="sans-serif">高质量对话体验</text>
      
      <rect x="570" y="665" width="200" height="150" fill="white" rx="8" stroke="#ea580c" stroke-width="2" filter="url(#shadow)"/>
      <text x="670" y="685" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">推理诊断智能体</text>
      <text x="670" y="700" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">(Reasoning Agent)</text>
      <rect x="580" y="710" width="60" height="40" fill="#e0e7ff" rx="4" stroke="#6366f1" stroke-width="1"/>
      <text x="610" y="725" text-anchor="middle" fill="#4338ca" font-size="9" font-weight="bold" font-family="sans-serif">SOP规划</text>
      <text x="610" y="740" text-anchor="middle" fill="#4338ca" font-size="8" font-family="sans-serif">动态调整</text>
      <rect x="650" y="710" width="60" height="40" fill="#e0e7ff" rx="4" stroke="#6366f1" stroke-width="1"/>
      <text x="680" y="725" text-anchor="middle" fill="#4338ca" font-size="9" font-weight="bold" font-family="sans-serif">多工具</text>
      <text x="680" y="740" text-anchor="middle" fill="#4338ca" font-size="8" font-family="sans-serif">协同调用</text>
      <rect x="720" y="710" width="60" height="40" fill="#e0e7ff" rx="4" stroke="#6366f1" stroke-width="1"/>
      <text x="750" y="725" text-anchor="middle" fill="#4338ca" font-size="9" font-weight="bold" font-family="sans-serif">失败反思</text>
      <text x="750" y="740" text-anchor="middle" fill="#4338ca" font-size="8" font-family="sans-serif">自动重试</text>
      <rect x="580" y="760" width="180" height="45" fill="#ffe4e6" rx="4" stroke="#f43f5e" stroke-width="1"/>
      <text x="670" y="775" text-anchor="middle" fill="#be123c" font-size="10" font-weight="bold" font-family="sans-serif">🎯 端到端成功率: 80%</text>
      <text x="670" y="793" text-anchor="middle" fill="#be123c" font-size="10" font-weight="bold" font-family="sans-serif">🎯 推理幻觉率: &lt;5%</text>
      
      <rect x="850" y="665" width="200" height="150" fill="white" rx="8" stroke="#ea580c" stroke-width="2" filter="url(#shadow)"/>
      <text x="950" y="685" text-anchor="middle" fill="#ea580c" font-size="14" font-weight="bold" font-family="sans-serif">测试智能体</text>
      <text x="950" y="700" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">(Test Agent)</text>
      <rect x="860" y="710" width="80" height="40" fill="#f3e8ff" rx="4" stroke="#8b5cf6" stroke-width="1"/>
      <text x="900" y="725" text-anchor="middle" fill="#6b21a8" font-size="9" font-weight="bold" font-family="sans-serif">智能用例生成</text>
      <text x="900" y="740" text-anchor="middle" fill="#6b21a8" font-size="8" font-family="sans-serif">代码+需求</text>
      <rect x="960" y="710" width="80" height="40" fill="#f3e8ff" rx="4" stroke="#8b5cf6" stroke-width="1"/>
      <text x="1000" y="725" text-anchor="middle" fill="#6b21a8" font-size="9" font-weight="bold" font-family="sans-serif">自动化编排</text>
      <text x="1000" y="740" text-anchor="middle" fill="#6b21a8" font-size="8" font-family="sans-serif">三新测试</text>
      <rect x="860" y="760" width="180" height="45" fill="#dbeafe" rx="4" stroke="#3b82f6" stroke-width="1"/>
      <text x="950" y="780" text-anchor="middle" fill="#1e40af" font-size="10" font-weight="bold" font-family="sans-serif">提升研发测试效率</text>
    </g>

    <!-- House Upper Floor (Interface & Integration) -->
    <g id="upper-floor">
      <rect x="260" y="280" width="820" height="180" fill="url(#grad-upper-floor)" rx="12" filter="url(#shadow)"/>
      <text x="280" y="310" fill="white" font-size="19" font-weight="bold" font-family="sans-serif">接口层 - 智能融入各大平台</text>
      <text x="750" y="310" fill="#cffafe" font-size="12" font-family="sans-serif">🎯 核心目标: 核心场景覆盖30%+, API响应P95 &lt;500ms</text>
      
      <rect x="280" y="330" width="190" height="110" fill="white" rx="8" stroke="#0891b2" stroke-width="2" filter="url(#shadow)"/>
      <text x="375" y="350" text-anchor="middle" fill="#0891b2" font-size="13" font-weight="bold" font-family="sans-serif">钉钉DingOps &amp; CIPU</text>
      <text x="375" y="370" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">@CloudBot唤醒服务</text>
      <text x="375" y="385" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">交互式诊断服务</text>
      <text x="375" y="400" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">诊断界面提供</text>
      
      <rect x="490" y="330" width="190" height="110" fill="white" rx="8" stroke="#0891b2" stroke-width="2" filter="url(#shadow)"/>
      <text x="585" y="350" text-anchor="middle" fill="#0891b2" font-size="13" font-weight="bold" font-family="sans-serif">Aone平台集成</text>
      <text x="585" y="370" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">工单流转自动应答</text>
      <text x="585" y="385" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">推理诊断报告推送</text>
      <text x="585" y="400" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">@CloudBot评论唤醒</text>
      
      <rect x="700" y="330" width="170" height="110" fill="white" rx="8" stroke="#0891b2" stroke-width="2" filter="url(#shadow)"/>
      <text x="785" y="350" text-anchor="middle" fill="#0891b2" font-size="13" font-weight="bold" font-family="sans-serif">AES客户端</text>
      <text x="785" y="370" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">智能应答能力增强</text>
      <text x="785" y="385" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">结构化诊断报告</text>
      
      <rect x="890" y="330" width="170" height="110" fill="white" rx="8" stroke="#0891b2" stroke-width="2" filter="url(#shadow)"/>
      <text x="975" y="350" text-anchor="middle" fill="#0891b2" font-size="13" font-weight="bold" font-family="sans-serif">CloudBot独立平台</text>
      <text x="975" y="370" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">定界诊断界面</text>
      <text x="975" y="385" text-anchor="middle" fill="#374151" font-size="10" font-family="sans-serif">客户视角诊断</text>
    </g>

    <!-- House Roof (Business Layer) -->
    <g id="roof">
      <polygon points="150,350 670,150 1190,350" fill="url(#grad-roof)" filter="url(#shadow)"/>
      <text x="670" y="185" text-anchor="middle" fill="white" font-size="18" font-weight="bold" font-family="sans-serif">业务层</text>
      
      <rect x="200" y="200" width="280" height="110" fill="white" rx="5" stroke="#dc2626" stroke-width="2"/>
      <text x="340" y="220" text-anchor="middle" fill="#dc2626" font-size="14" font-weight="bold" font-family="sans-serif">工单智能体 (服务无人化)</text>
      <text x="340" y="245" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 工单诊断准确率: 40% → 60%</text>
      <text x="340" y="265" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 根因定位率: 80%+</text>
      <text x="340" y="285" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 SOP覆盖率: 90%+</text>
      
      <rect x="530" y="200" width="280" height="110" fill="white" rx="5" stroke="#dc2626" stroke-width="2"/>
      <text x="670" y="220" text-anchor="middle" fill="#dc2626" font-size="14" font-weight="bold" font-family="sans-serif">诊断助手 (规模化AI服务)</text>
      <text x="670" y="255" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 日均UV增长: 10倍</text>
      <text x="670" y="275" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 渗透率: 50%+</text>

      <rect x="860" y="200" width="280" height="110" fill="white" rx="5" stroke="#dc2626" stroke-width="2"/>
      <text x="1000" y="220" text-anchor="middle" fill="#dc2626" font-size="14" font-weight="bold" font-family="sans-serif">测试智能体 (革新研发测试)</text>
      <text x="1000" y="255" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 覆盖率提升: +30%</text>
      <text x="1000" y="275" text-anchor="middle" fill="#374151" font-size="11" font-family="sans-serif">🎯 构建效率提升: +50%</text>
    </g>

  </g>
</svg>