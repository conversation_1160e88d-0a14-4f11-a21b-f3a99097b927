<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VM性能监控仪表板 - 修复版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">VM性能监控仪表板</h1>
                    <p class="text-sm text-gray-500 mt-1">实例: eci-0xi16p1femf8dbmxdqpp | 数据来源: 真实监控数据</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">数据已加载</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">存储IOPS</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-iops">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">网络PPS</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-pps">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">平均延迟</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-latency">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">丢包率</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-drop-ratio">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 存储IOPS -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">存储IOPS</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">读取</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">写入</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="iopsChart"></canvas>
                </div>
            </div>

            <!-- 存储延迟 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">存储延迟 (ms)</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">读延迟</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">写延迟</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="latencyChart"></canvas>
                </div>
            </div>

            <!-- 网络PPS -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">网络PPS</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">接收</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">发送</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>

            <!-- 系统指标 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">系统指标</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">内存带宽</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">丢包率</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="systemChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 数据加载状态 -->
        <div id="loadingStatus" class="mt-8 text-center text-gray-500">
            正在加载数据...
        </div>
    </main>

    <script>
        // 从JSON文件加载数据
        async function loadData() {
            try {
                const response = await fetch('src/vm_data_output/file_system/req_5360df80-6e2d-416a-abf6-9bdb939f99b5/vm_performance_eci-0xi16p1femf8dbmxdqpp_20250901_193622.json');
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('加载数据失败，使用示例数据:', error);
                // 返回示例数据
                return getSampleData();
            }
        }

        // 示例数据（从你的JSON文件提取）
        function getSampleData() {
            return {
                "VmVportDropMetric/drop_ratio": [
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.0},
                    {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.0},
                    {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.0},
                    {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0002907681377941676},
                    {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.002600927178146232}
                ],
                "VmStorageIOLatency/read_iops": [
                    {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 456.6},
                    {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 523.2},
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 489.1},
                    {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 512.8},
                    {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 478.3}
                ],
                "VmStorageIOLatency/write_iops": [
                    {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 1446.266667},
                    {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 1523.445},
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 1389.123},
                    {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 1467.892},
                    {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 1398.567}
                ],
                "VmStorageIOLatency/read_lat_ms": [
                    {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 0.8},
                    {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 0.7},
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.9},
                    {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 0.6},
                    {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 0.8}
                ],
                "VmStorageIOLatency/write_lat_ms": [
                    {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 1.0},
                    {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 1.2},
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.9},
                    {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 1.1},
                    {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 1.0}
                ],
                "VmPpsBps/rx_pps": [
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 8907.854},
                    {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 9234.123},
                    {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 8756.432},
                    {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 9012.567},
                    {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 8834.789}
                ],
                "VmPpsBps/tx_pps": [
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 677.185},
                    {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 723.456},
                    {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 654.321},
                    {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 698.234},
                    {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 712.567}
                ],
                "VmMemBWMetric/memory_bw": [
                    {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 35.0},
                    {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 42.5},
                    {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 38.7},
                    {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 41.2},
                    {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 39.8}
                ]
            };
        }

        // 工具函数
        function parseDateTime(dateTimeStr) {
            // 将 "2025-08-28 00:33:00" 格式转换为 Date 对象
            return new Date(dateTimeStr.replace(' ', 'T'));
        }

        function calculateStats(data) {
            const values = data.map(d => d.value);
            return {
                min: Math.min(...values),
                max: Math.max(...values),
                avg: values.reduce((a, b) => a + b, 0) / values.length,
                count: values.length
            };
        }

        function formatNumber(num, decimals = 2) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toFixed(decimals);
        }

        // 创建图表配置
        function createChartConfig(datasets, yAxisLabel) {
            return {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            callbacks: {
                                title: function(context) {
                                    return new Date(context[0].parsed.x).toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '时间'
                            },
                            ticks: {
                                callback: function(value, index, values) {
                                    // 显示时间格式
                                    const date = new Date(value);
                                    return date.toLocaleTimeString('zh-CN', {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    });
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: yAxisLabel
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.4
                        },
                        point: {
                            radius: 3,
                            hoverRadius: 6
                        }
                    }
                }
            };
        }

        // 初始化图表
        function initCharts(performanceData) {
            // 存储IOPS图表
            if (performanceData['VmStorageIOLatency/read_iops'] && performanceData['VmStorageIOLatency/write_iops']) {
                const iopsCtx = document.getElementById('iopsChart').getContext('2d');
                new Chart(iopsCtx, createChartConfig([
                    {
                        label: '读取IOPS',
                        data: performanceData['VmStorageIOLatency/read_iops'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true
                    },
                    {
                        label: '写入IOPS',
                        data: performanceData['VmStorageIOLatency/write_iops'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        fill: true
                    }
                ], 'IOPS'));
            }

            // 存储延迟图表
            if (performanceData['VmStorageIOLatency/read_lat_ms'] && performanceData['VmStorageIOLatency/write_lat_ms']) {
                const latencyCtx = document.getElementById('latencyChart').getContext('2d');
                new Chart(latencyCtx, createChartConfig([
                    {
                        label: '读延迟',
                        data: performanceData['VmStorageIOLatency/read_lat_ms'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(147, 51, 234)',
                        backgroundColor: 'rgba(147, 51, 234, 0.1)',
                        fill: true
                    },
                    {
                        label: '写延迟',
                        data: performanceData['VmStorageIOLatency/write_lat_ms'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(236, 72, 153)',
                        backgroundColor: 'rgba(236, 72, 153, 0.1)',
                        fill: true
                    }
                ], '延迟 (ms)'));
            }

            // 网络PPS图表
            if (performanceData['VmPpsBps/rx_pps'] && performanceData['VmPpsBps/tx_pps']) {
                const networkCtx = document.getElementById('networkChart').getContext('2d');
                new Chart(networkCtx, createChartConfig([
                    {
                        label: '接收PPS',
                        data: performanceData['VmPpsBps/rx_pps'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(99, 102, 241)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        fill: true
                    },
                    {
                        label: '发送PPS',
                        data: performanceData['VmPpsBps/tx_pps'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(6, 182, 212)',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        fill: true
                    }
                ], 'PPS'));
            }

            // 系统指标图表
            if (performanceData['VmMemBWMetric/memory_bw'] && performanceData['VmVportDropMetric/drop_ratio']) {
                const systemCtx = document.getElementById('systemChart').getContext('2d');
                new Chart(systemCtx, createChartConfig([
                    {
                        label: '内存带宽',
                        data: performanceData['VmMemBWMetric/memory_bw'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value
                        })),
                        borderColor: 'rgb(249, 115, 22)',
                        backgroundColor: 'rgba(249, 115, 22, 0.1)',
                        fill: true
                    },
                    {
                        label: '丢包率 (%)',
                        data: performanceData['VmVportDropMetric/drop_ratio'].map(d => ({
                            x: parseDateTime(d.datetime).getTime(),
                            y: d.value * 100
                        })),
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        fill: true
                    }
                ], '混合指标'));
            }
        }

        // 更新概览卡片
        function updateOverviewCards(performanceData) {
            // 计算平均IOPS
            if (performanceData['VmStorageIOLatency/read_iops'] && performanceData['VmStorageIOLatency/write_iops']) {
                const readIOPS = calculateStats(performanceData['VmStorageIOLatency/read_iops']);
                const writeIOPS = calculateStats(performanceData['VmStorageIOLatency/write_iops']);
                const avgIOPS = (readIOPS.avg + writeIOPS.avg) / 2;
                document.getElementById('avg-iops').textContent = formatNumber(avgIOPS, 0);
            }

            // 计算平均PPS
            if (performanceData['VmPpsBps/rx_pps'] && performanceData['VmPpsBps/tx_pps']) {
                const rxPPS = calculateStats(performanceData['VmPpsBps/rx_pps']);
                const txPPS = calculateStats(performanceData['VmPpsBps/tx_pps']);
                const avgPPS = (rxPPS.avg + txPPS.avg) / 2;
                document.getElementById('avg-pps').textContent = formatNumber(avgPPS, 0);
            }

            // 计算平均延迟
            if (performanceData['VmStorageIOLatency/read_lat_ms'] && performanceData['VmStorageIOLatency/write_lat_ms']) {
                const readLat = calculateStats(performanceData['VmStorageIOLatency/read_lat_ms']);
                const writeLat = calculateStats(performanceData['VmStorageIOLatency/write_lat_ms']);
                const avgLatency = (readLat.avg + writeLat.avg) / 2;
                document.getElementById('avg-latency').textContent = avgLatency.toFixed(2) + ' ms';
            }

            // 计算平均丢包率
            if (performanceData['VmVportDropMetric/drop_ratio']) {
                const dropRatio = calculateStats(performanceData['VmVportDropMetric/drop_ratio']);
                document.getElementById('avg-drop-ratio').textContent = (dropRatio.avg * 100).toFixed(4) + '%';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                const performanceData = await loadData();
                console.log('数据加载成功:', Object.keys(performanceData));
                
                initCharts(performanceData);
                updateOverviewCards(performanceData);
                
                document.getElementById('loadingStatus').textContent = `数据加载完成，共 ${Object.keys(performanceData).length} 个指标`;
                setTimeout(() => {
                    document.getElementById('loadingStatus').style.display = 'none';
                }, 3000);
                
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('loadingStatus').textContent = '数据加载失败，请检查控制台';
            }
        });
    </script>
</body>
</html>