"""
ReasoningAgent Chat V1 API 集成测试

测试 ReasoningAgent 的 SSE 事件，确保与 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import pytest
import os
import sys
from typing import Dict, Any
from pathlib import Path

# 添加当前目录到 Python 路径，以支持直接运行
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from base_api_test_framework import ChatV1APITester

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class ReasoningAgentTester(ChatV1APITester):
    """ReasoningAgent 专用测试器"""
    
    async def test_reasoning_agent_sse_events(self, question: str, save_to_file: bool = True) -> Dict[str, Any]:
        """
        测试 ReasoningAgent 的 SSE 事件（使用通用流式方法），并统计时延信息。
        """
        request_data = {
            "question": question,
            "agent": "ReasoningAgent",
            "user_id": "149789",
        }
        return await self._stream_chat(request_data, save_to_file=save_to_file, file_prefix="reasoning_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_sse_events_e2e():
    """
    ReasoningAgent SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "利用vmcore分析这个NC ************* 在20250724的04:00宕机原因"
    test_question = "利用vmcore分析这个NC *********** 在20250826的宕机原因"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent SSE 事件...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_capabilities_question():
    """
    测试 ReasoningAgent 回答能力相关问题
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 能力问题测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "你有哪些能力呢"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 能力问题...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent 能力问题测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_health_report_query():
    """
    测试 ReasoningAgent 查询健康报告
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 健康报告查询测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "查询用户aliUid 1781574661016173的最近2天健康报告"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 健康报告查询...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent 健康报告查询测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_instance_performance_analysis():
    """
    测试 ReasoningAgent 实例性能数据采集与异常诊断
    
    测试场景：
    1. 实例性能异常诊断
    2. 查询实例和NC信息
    3. 检查监控异常和NC状态
    4. 采集性能数据并生成图表
    5. 验证SOP流程的完整执行
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 实例性能分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据 - 模拟实例性能异常问题
    test_question = "实例eci-0xi16p1femf8dbmxdqpp在2025-08-28 00:00:00至2025-08-28 05:00:00期间出现性能异常，需要分析CPU和内存使用情况"
    print(f"📝 测试问题: {test_question}")
    print("🎯 预期触发SOP: instance_performance_analysis")
    print("📋 预期执行步骤:")
    print("   1. 实例性能异常诊断 (runPerformanceDiagnose)")
    print("   2. 查询实例对应的NC信息 (getVmBasicInfo)")
    print("   3. 检查实例异常状态 (listMonitorExceptions)")
    print("   4. 检查NC异常状态 (getNcDownRecord)")
    print("   5. 采集性能数据并生成图表信息 (coder)")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 实例性能分析...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        # 额外验证性能分析特定内容
        print("🔍 验证性能分析特定内容...")
        
        # 检查是否包含性能相关关键词
        response_text = str(result.get('messages', []))
        performance_keywords = [
            "性能", "CPU", "内存", "IO", "网络", 
            "runPerformanceDiagnose", "getVmBasicInfo", 
            "listMonitorExceptions", "getNcDownRecord",
            "collect_vm_data", "time_series"
        ]
        
        found_keywords = []
        for keyword in performance_keywords:
            if keyword in response_text:
                found_keywords.append(keyword)
        
        print(f"✅ 找到性能相关关键词: {found_keywords}")
        
        # 检查是否有代码生成相关内容
        if "coder" in response_text or "collect_vm_data" in response_text:
            print("✅ 检测到代码生成步骤")
        
        # 检查是否有数据采集相关内容
        if "time_series" in response_text or "性能数据" in response_text:
            print("✅ 检测到性能数据采集内容")
        
        print("🎉 ReasoningAgent 实例性能分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_performance_analysis_with_specific_metrics():
    """
    测试 ReasoningAgent 特定指标的性能分析
    
    测试场景：测试针对特定性能指标（如IO延迟）的分析
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 特定指标性能分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据 - 模拟IO性能问题
    test_question = "eci-0xi16p1femf8dbmxdqpp在2025-08-28 00:00:00至2025-08-28 05:00:00期间出现性能异常"
    print(f"📝 测试问题: {test_question}")
    print("🎯 预期触发SOP: instance_performance_analysis")
    print("📋 预期关注指标: VmStorageIOLatency/read_lat_ms, VmStorageIOLatency/write_lat_ms")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 特定指标性能分析...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        # 验证IO相关内容
        response_text = str(result.get('messages', []))
        io_keywords = [
            "IO", "延迟", "VmStorageIOLatency", 
            "read_lat_ms", "write_lat_ms", "IOPS"
        ]
        
        found_io_keywords = []
        for keyword in io_keywords:
            if keyword in response_text:
                found_io_keywords.append(keyword)
        
        print(f"✅ 找到IO相关关键词: {found_io_keywords}")
        
        print("🎉 ReasoningAgent 特定指标性能分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行 ReasoningAgent 测试
    
    运行方式：
    python tests/integration/api/test_reasoning_agent.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_reasoning_agent.py -v -s
    
    # 运行特定测试
    pytest tests/integration/api/test_reasoning_agent.py::test_reasoning_agent_sse_events_e2e -v -s
    
    # 运行性能分析测试
    pytest tests/integration/api/test_reasoning_agent.py::test_reasoning_agent_instance_performance_analysis -v -s
    """
    pytest.main([__file__, "-v", "-s", "--tb=short"])
    print("ReasoningAgent 测试完成！")
