<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VM性能监控仪表板 - 真实数据</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">VM性能监控仪表板</h1>
                    <p class="text-sm text-gray-500 mt-1">实例: eci-0xi16p1femf8dbmxdqpp | 数据来源: 真实监控数据</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">数据已加载</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">存储IOPS</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-iops">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">网络PPS</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-pps">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">平均延迟</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-latency">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">丢包率</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-drop-ratio">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 存储IOPS -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">存储IOPS</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">读取</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">写入</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="iopsChart"></canvas>
                </div>
            </div>

            <!-- 存储延迟 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">存储延迟 (ms)</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">读延迟</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">写延迟</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="latencyChart"></canvas>
                </div>
            </div>

            <!-- 网络PPS -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">网络PPS</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">接收</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">发送</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>

            <!-- 系统指标 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">系统指标</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">内存带宽</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">丢包率</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="systemChart"></canvas>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 真实VM性能数据
        const performanceData = {"VmVportDropMetric/drop_ratio": [{"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.0}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.0}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.0}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0002907681377941676}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.002600927178146232}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.0}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.0}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.0}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.0033548910785121512}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.0}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 0.0}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 0.0003362650183143489}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 0.0}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 0.003280349862048264}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 0.0}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 0.0}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 0.0}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 0.0}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 0.024777286775031247}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 0.0}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 0.0}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 0.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 2.586672477994335}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 0.0}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 0.0}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 0.0}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 0.0}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 0.000749187915885606}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 0.0}, {"timestamp": 1756314480, "datetime": "2025-08-28 01:08:00", "value": 0.0006148413458432788}], "VmStealMetric/vmsteal": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 0.0}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 0.0}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.08}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 0.02}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 0.02}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.03}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 0.14}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.13}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.07}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.04}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 0.08}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.13}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.13}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.11}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.12}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.11}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 0.11}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 0.1}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 0.08}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 0.1}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 0.06}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 0.1}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 0.12}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 0.07}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 0.03}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 0.0}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 0.0}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 0.0}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 0.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 0.0}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 0.0}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 0.0}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 0.06}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 0.07}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 0.07}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 0.13}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 0.09}, {"timestamp": 1756314480, "datetime": "2025-08-28 01:08:00", "value": 0.1}], "VmStorageIOLatency/read_iops": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 456.6}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 36.0}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 19.3}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 1.033333}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 0.066667}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.066667}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 0.0}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 3.633333}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.0}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 0.0}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.0}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.0}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.0}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.0}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.133333}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 0.0}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 0.0}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 0.0}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 0.0}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 0.0}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 0.0}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 0.0}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 0.0}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 0.366667}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 0.0}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 0.0}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 0.0}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 0.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 0.033333}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 0.366667}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 0.233333}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 0.0}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 0.0}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 0.0}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 0.0}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 0.0}], "VmMemBWMetric/memory_bw": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 35.0}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 5023.0}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 8343.0}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 8091.0}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 7435.0}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 9643.0}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 9008.0}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 9745.0}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 9846.0}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 9806.0}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 10253.0}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 10091.0}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 9116.0}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 8535.0}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 7962.0}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 10127.0}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 9914.0}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 7825.0}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 8840.0}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 9451.0}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 9017.0}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 10332.0}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 9711.0}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 9395.0}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 43.0}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 80.0}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 76.0}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 70.0}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 70.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 81.0}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 54.0}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 111.0}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 6923.0}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 6737.0}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 6531.0}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 6459.0}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 6565.0}, {"timestamp": 1756314480, "datetime": "2025-08-28 01:08:00", "value": 23.0}], "VmStealMetric/vcpucpu": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 0.0}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 10.3125}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 99.61250000000001}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 98.84499999999998}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 99.61250000000001}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 99.5575}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 99.345}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 99.385}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 98.9125}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 99.56}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 99.44}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 99.6225}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 99.3475}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 98.84}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 99.305}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 99.65}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 99.4575}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 99.435}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 99.2575}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 98.79999999999998}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 99.1825}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 98.03}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 99.4225}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 98.91749999999999}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 39.34}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 0.4625}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 0.5175000000000001}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 0.4725}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 0.51}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 0.475}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 0.5}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 0.48}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 88.3275}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 91.5625}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 90.88000000000001}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 91.6175}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 90.2775}, {"timestamp": 1756314480, "datetime": "2025-08-28 01:08:00", "value": 50.8725}], "VmPpsBps/rx_pps": [{"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 8907.854}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 8791.08}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 9119.189}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 9538.423}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 9826.189}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 9827.696}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 9384.017}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 8669.0}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 8749.703000000001}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 8725.929}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 8688.979000000001}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 8594.188999999998}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 8904.134}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 9615.634}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 9604.715}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 9796.657000000001}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 9461.948}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 9424.576000000001}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 942.4630000000001}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 4.117}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 3.811}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 3.977}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 4.283}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 3.707}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 17641.246}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 36800.082}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 36556.384}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 38797.655}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 34220.168}, {"timestamp": 1756314480, "datetime": "2025-08-28 01:08:00", "value": 4274.3279999999995}], "VmStorageIOLatency/write_lat_ms": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 1.0}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 0.0}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 1.0}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 0.0}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 0.0}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.0}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 0.0}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.0}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.0}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 0.0}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.0}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.0}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.0}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.0}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.0}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 0.0}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 1.0}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 0.0}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 0.0}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 0.0}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 0.0}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 0.0}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 0.0}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 0.0}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 0.0}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 0.0}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 0.0}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 0.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 0.0}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 0.0}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 0.0}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 0.0}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 0.0}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 0.0}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 0.0}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 0.0}], "VmStorageIOLatency/read_lat_ms": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 0.0}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 0.0}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.0}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 0.0}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 0.0}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.0}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 0.0}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.0}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.0}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 0.0}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.0}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.0}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.0}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.0}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.0}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 0.0}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 0.0}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 0.0}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 0.0}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 0.0}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 0.0}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 0.0}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 0.0}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 0.0}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 0.0}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 0.0}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 0.0}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 0.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 0.0}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 0.0}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 0.0}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 0.0}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 0.0}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 0.0}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 0.0}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 0.0}], "VmStorageIOLatency/write_iops": [{"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 1446.266667}, {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 4.866667}, {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 19.3}, {"timestamp": 1756312440, "datetime": "2025-08-28 00:34:00", "value": 25.866667}, {"timestamp": 1756312500, "datetime": "2025-08-28 00:35:00", "value": 23.066667}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 27.533333}, {"timestamp": 1756312620, "datetime": "2025-08-28 00:37:00", "value": 28.3}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 33.433333}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 43.8}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 31.933333}, {"timestamp": 1756312860, "datetime": "2025-08-28 00:41:00", "value": 39.4}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 38.933333}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 50.066667}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 45.233333}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 29.033333}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 48.066667}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 39.3}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 36.533333}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 39.466667}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 62.733333}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 50.033333}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 44.3}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 43.966667}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 62.6}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 33.366667}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 4.233333}, {"timestamp": 1756313820, "datetime": "2025-08-28 00:57:00", "value": 2.6}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 3.2}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 3.0}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 3.7}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 4.3}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 13.766667}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 21.466667}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 18.266667}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 14.833333}, {"timestamp": 1756314360, "datetime": "2025-08-28 01:06:00", "value": 27.633333}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 12.733333}], "VmPpsBps/tx_pps": [{"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 677.1850000000001}, {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 582.0730000000001}, {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 523.666}, {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 779.076}, {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 862.307}, {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 861.6959999999999}, {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 829.807}, {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 521.398}, {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 848.2239999999999}, {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 820.2139999999999}, {"timestamp": 1756313220, "datetime": "2025-08-28 00:47:00", "value": 695.1120000000001}, {"timestamp": 1756313280, "datetime": "2025-08-28 00:48:00", "value": 624.7299999999999}, {"timestamp": 1756313340, "datetime": "2025-08-28 00:49:00", "value": 942.221}, {"timestamp": 1756313400, "datetime": "2025-08-28 00:50:00", "value": 749.114}, {"timestamp": 1756313460, "datetime": "2025-08-28 00:51:00", "value": 769.174}, {"timestamp": 1756313520, "datetime": "2025-08-28 00:52:00", "value": 811.62}, {"timestamp": 1756313580, "datetime": "2025-08-28 00:53:00", "value": 698.2959999999999}, {"timestamp": 1756313640, "datetime": "2025-08-28 00:54:00", "value": 888.943}, {"timestamp": 1756313700, "datetime": "2025-08-28 00:55:00", "value": 199.71200000000002}, {"timestamp": 1756313760, "datetime": "2025-08-28 00:56:00", "value": 5.796}, {"timestamp": 1756313880, "datetime": "2025-08-28 00:58:00", "value": 5.3420000000000005}, {"timestamp": 1756313940, "datetime": "2025-08-28 00:59:00", "value": 5.527}, {"timestamp": 1756314000, "datetime": "2025-08-28 01:00:00", "value": 6.271}, {"timestamp": 1756314060, "datetime": "2025-08-28 01:01:00", "value": 5.154}, {"timestamp": 1756314120, "datetime": "2025-08-28 01:02:00", "value": 650.979}, {"timestamp": 1756314180, "datetime": "2025-08-28 01:03:00", "value": 1178.9740000000002}, {"timestamp": 1756314240, "datetime": "2025-08-28 01:04:00", "value": 1274.234}, {"timestamp": 1756314300, "datetime": "2025-08-28 01:05:00", "value": 1245.703}, {"timestamp": 1756314420, "datetime": "2025-08-28 01:07:00", "value": 1363.224}, {"timestamp": 1756314480, "datetime": "2025-08-28 01:08:00", "value": 767.623}]};

        // 工具函数
        function calculateStats(data) {
            const values = data.map(d => d.value);
            return {
                min: Math.min(...values),
                max: Math.max(...values),
                avg: values.reduce((a, b) => a + b, 0) / values.length,
                count: values.length
            };
        }

        function formatNumber(num, decimals = 2) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toFixed(decimals);
        }

        // 创建图表配置
        function createChartConfig(datasets, yAxisLabel) {
            return {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                parser: 'yyyy-MM-dd HH:mm:ss',
                                displayFormats: {
                                    minute: 'HH:mm',
                                    hour: 'HH:mm'
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: yAxisLabel
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.4
                        },
                        point: {
                            radius: 3,
                            hoverRadius: 6
                        }
                    }
                }
            };
        }

        // 初始化图表
        function initCharts() {
            // 存储IOPS图表
            if (performanceData['VmStorageIOLatency/read_iops'] && performanceData['VmStorageIOLatency/write_iops']) {
                const iopsCtx = document.getElementById('iopsChart').getContext('2d');
                new Chart(iopsCtx, createChartConfig([
                    {
                        label: '读取IOPS',
                        data: performanceData['VmStorageIOLatency/read_iops'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true
                    },
                    {
                        label: '写入IOPS',
                        data: performanceData['VmStorageIOLatency/write_iops'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        fill: true
                    }
                ], 'IOPS'));
            }

            // 存储延迟图表
            if (performanceData['VmStorageIOLatency/read_lat_ms'] && performanceData['VmStorageIOLatency/write_lat_ms']) {
                const latencyCtx = document.getElementById('latencyChart').getContext('2d');
                new Chart(latencyCtx, createChartConfig([
                    {
                        label: '读延迟',
                        data: performanceData['VmStorageIOLatency/read_lat_ms'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(147, 51, 234)',
                        backgroundColor: 'rgba(147, 51, 234, 0.1)',
                        fill: true
                    },
                    {
                        label: '写延迟',
                        data: performanceData['VmStorageIOLatency/write_lat_ms'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(236, 72, 153)',
                        backgroundColor: 'rgba(236, 72, 153, 0.1)',
                        fill: true
                    }
                ], '延迟 (ms)'));
            }

            // 网络PPS图表
            if (performanceData['VmPpsBps/rx_pps'] && performanceData['VmPpsBps/tx_pps']) {
                const networkCtx = document.getElementById('networkChart').getContext('2d');
                new Chart(networkCtx, createChartConfig([
                    {
                        label: '接收PPS',
                        data: performanceData['VmPpsBps/rx_pps'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(99, 102, 241)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        fill: true
                    },
                    {
                        label: '发送PPS',
                        data: performanceData['VmPpsBps/tx_pps'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(6, 182, 212)',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        fill: true
                    }
                ], 'PPS'));
            }

            // 系统指标图表
            if (performanceData['VmMemBWMetric/memory_bw'] && performanceData['VmVportDropMetric/drop_ratio']) {
                const systemCtx = document.getElementById('systemChart').getContext('2d');
                new Chart(systemCtx, createChartConfig([
                    {
                        label: '内存带宽',
                        data: performanceData['VmMemBWMetric/memory_bw'].map(d => ({
                            x: d.datetime,
                            y: d.value
                        })),
                        borderColor: 'rgb(249, 115, 22)',
                        backgroundColor: 'rgba(249, 115, 22, 0.1)',
                        fill: true
                    },
                    {
                        label: '丢包率 (%)',
                        data: performanceData['VmVportDropMetric/drop_ratio'].map(d => ({
                            x: d.datetime,
                            y: d.value * 100
                        })),
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        fill: true
                    }
                ], '混合指标'));
            }
        }

        // 更新概览卡片
        function updateOverviewCards() {
            // 计算平均IOPS
            if (performanceData['VmStorageIOLatency/read_iops'] && performanceData['VmStorageIOLatency/write_iops']) {
                const readIOPS = calculateStats(performanceData['VmStorageIOLatency/read_iops']);
                const writeIOPS = calculateStats(performanceData['VmStorageIOLatency/write_iops']);
                const avgIOPS = (readIOPS.avg + writeIOPS.avg) / 2;
                document.getElementById('avg-iops').textContent = formatNumber(avgIOPS, 0);
            }

            // 计算平均PPS
            if (performanceData['VmPpsBps/rx_pps'] && performanceData['VmPpsBps/tx_pps']) {
                const rxPPS = calculateStats(performanceData['VmPpsBps/rx_pps']);
                const txPPS = calculateStats(performanceData['VmPpsBps/tx_pps']);
                const avgPPS = (rxPPS.avg + txPPS.avg) / 2;
                document.getElementById('avg-pps').textContent = formatNumber(avgPPS, 0);
            }

            // 计算平均延迟
            if (performanceData['VmStorageIOLatency/read_lat_ms'] && performanceData['VmStorageIOLatency/write_lat_ms']) {
                const readLat = calculateStats(performanceData['VmStorageIOLatency/read_lat_ms']);
                const writeLat = calculateStats(performanceData['VmStorageIOLatency/write_lat_ms']);
                const avgLatency = (readLat.avg + writeLat.avg) / 2;
                document.getElementById('avg-latency').textContent = avgLatency.toFixed(2) + ' ms';
            }

            // 计算平均丢包率
            if (performanceData['VmVportDropMetric/drop_ratio']) {
                const dropRatio = calculateStats(performanceData['VmVportDropMetric/drop_ratio']);
                document.getElementById('avg-drop-ratio').textContent = (dropRatio.avg * 100).toFixed(4) + '%';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            updateOverviewCards();
        });
    </script>
</body>
</html>