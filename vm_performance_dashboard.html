<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VM性能监控仪表板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">VM性能监控仪表板</h1>
                    <p class="text-sm text-gray-500 mt-1">实例: eci-0xi16p1femf8dbmxdqpp | 时间: 2025-09-01 19:36:22</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">实时监控</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">存储IOPS</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-iops">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">网络PPS</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-pps">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">平均延迟</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-latency">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">丢包率</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-drop-ratio">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 存储性能 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">存储IOPS</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">读取</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">写入</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="iopsChart"></canvas>
                </div>
            </div>

            <!-- 存储延迟 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">存储延迟 (ms)</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">读延迟</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">写延迟</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="latencyChart"></canvas>
                </div>
            </div>

            <!-- 网络性能 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">网络PPS</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">接收</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">发送</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>

            <!-- 系统指标 -->
            <div class="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">系统指标</h3>
                    <div class="flex space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">内存带宽</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">丢包率</span>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="systemChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">详细数据</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">指标</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据点数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最小值</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最大值</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均值</th>
                        </tr>
                    </thead>
                    <tbody id="dataTable" class="bg-white divide-y divide-gray-200">
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        // 性能数据
        const performanceData = {
            "VmVportDropMetric/drop_ratio": [
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.0},
                {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.0},
                {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.0},
                {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0002907681377941676},
                {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.002600927178146232},
                {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.0},
                {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.0},
                {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.0},
                {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.0033548910785121512},
                {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.0}
            ],
            "VmStealMetric/vmsteal": [
                {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 0.0},
                {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 0.0},
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.0}
            ],
            "VmStorageIOLatency/read_iops": [
                {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 456.6},
                {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 523.2},
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 489.1}
            ],
            "VmMemBWMetric/memory_bw": [
                {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 35.0},
                {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 42.5},
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 38.7}
            ],
            "VmPpsBps/rx_pps": [
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 8907.854},
                {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 9234.123},
                {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 8756.432}
            ],
            "VmStorageIOLatency/write_lat_ms": [
                {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 1.0},
                {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 1.2},
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.9}
            ],
            "VmStorageIOLatency/read_lat_ms": [
                {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 0.8},
                {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 0.7},
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.9}
            ],
            "VmStorageIOLatency/write_iops": [
                {"timestamp": 1756312260, "datetime": "2025-08-28 00:31:00", "value": 1446.266667},
                {"timestamp": 1756312320, "datetime": "2025-08-28 00:32:00", "value": 1523.445},
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 1389.123}
            ],
            "VmPpsBps/tx_pps": [
                {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 677.185},
                {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 723.456},
                {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 654.321}
            ]
        };

        // 工具函数
        function formatTime(datetime) {
            return datetime.split(' ')[1]; // 只返回时间部分
        }

        function calculateStats(data) {
            const values = data.map(d => d.value);
            return {
                min: Math.min(...values),
                max: Math.max(...values),
                avg: values.reduce((a, b) => a + b, 0) / values.length,
                count: values.length
            };
        }

        function formatNumber(num, decimals = 2) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toFixed(decimals);
        }

        // 创建图表的通用配置
        function createChartConfig(datasets, yAxisLabel) {
            return {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                parser: 'YYYY-MM-DD HH:mm:ss',
                                displayFormats: {
                                    minute: 'HH:mm',
                                    hour: 'HH:mm'
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: yAxisLabel
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.4
                        },
                        point: {
                            radius: 3,
                            hoverRadius: 6
                        }
                    }
                }
            };
        }

        // 初始化图表
        function initCharts() {
            // IOPS图表
            const iopsCtx = document.getElementById('iopsChart').getContext('2d');
            new Chart(iopsCtx, createChartConfig([
                {
                    label: '读取IOPS',
                    data: performanceData['VmStorageIOLatency/read_iops'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true
                },
                {
                    label: '写入IOPS',
                    data: performanceData['VmStorageIOLatency/write_iops'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    fill: true
                }
            ], 'IOPS'));

            // 延迟图表
            const latencyCtx = document.getElementById('latencyChart').getContext('2d');
            new Chart(latencyCtx, createChartConfig([
                {
                    label: '读延迟',
                    data: performanceData['VmStorageIOLatency/read_lat_ms'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(147, 51, 234)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    fill: true
                },
                {
                    label: '写延迟',
                    data: performanceData['VmStorageIOLatency/write_lat_ms'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(236, 72, 153)',
                    backgroundColor: 'rgba(236, 72, 153, 0.1)',
                    fill: true
                }
            ], '延迟 (ms)'));

            // 网络图表
            const networkCtx = document.getElementById('networkChart').getContext('2d');
            new Chart(networkCtx, createChartConfig([
                {
                    label: '接收PPS',
                    data: performanceData['VmPpsBps/rx_pps'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(99, 102, 241)',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    fill: true
                },
                {
                    label: '发送PPS',
                    data: performanceData['VmPpsBps/tx_pps'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(6, 182, 212)',
                    backgroundColor: 'rgba(6, 182, 212, 0.1)',
                    fill: true
                }
            ], 'PPS'));

            // 系统指标图表
            const systemCtx = document.getElementById('systemChart').getContext('2d');
            new Chart(systemCtx, createChartConfig([
                {
                    label: '内存带宽',
                    data: performanceData['VmMemBWMetric/memory_bw'].map(d => ({
                        x: d.datetime,
                        y: d.value
                    })),
                    borderColor: 'rgb(249, 115, 22)',
                    backgroundColor: 'rgba(249, 115, 22, 0.1)',
                    fill: true,
                    yAxisID: 'y'
                },
                {
                    label: '丢包率 (%)',
                    data: performanceData['VmVportDropMetric/drop_ratio'].map(d => ({
                        x: d.datetime,
                        y: d.value * 100 // 转换为百分比
                    })),
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    fill: true,
                    yAxisID: 'y1'
                }
            ], '混合指标'));

            // 为系统图表添加双Y轴
            systemCtx.chart.options.scales.y1 = {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: '丢包率 (%)'
                },
                grid: {
                    drawOnChartArea: false,
                }
            };
        }

        // 更新概览卡片
        function updateOverviewCards() {
            // 计算平均IOPS
            const readIOPS = calculateStats(performanceData['VmStorageIOLatency/read_iops']);
            const writeIOPS = calculateStats(performanceData['VmStorageIOLatency/write_iops']);
            const avgIOPS = (readIOPS.avg + writeIOPS.avg) / 2;
            document.getElementById('avg-iops').textContent = formatNumber(avgIOPS, 0);

            // 计算平均PPS
            const rxPPS = calculateStats(performanceData['VmPpsBps/rx_pps']);
            const txPPS = calculateStats(performanceData['VmPpsBps/tx_pps']);
            const avgPPS = (rxPPS.avg + txPPS.avg) / 2;
            document.getElementById('avg-pps').textContent = formatNumber(avgPPS, 0);

            // 计算平均延迟
            const readLat = calculateStats(performanceData['VmStorageIOLatency/read_lat_ms']);
            const writeLat = calculateStats(performanceData['VmStorageIOLatency/write_lat_ms']);
            const avgLatency = (readLat.avg + writeLat.avg) / 2;
            document.getElementById('avg-latency').textContent = avgLatency.toFixed(2) + ' ms';

            // 计算平均丢包率
            const dropRatio = calculateStats(performanceData['VmVportDropMetric/drop_ratio']);
            document.getElementById('avg-drop-ratio').textContent = (dropRatio.avg * 100).toFixed(4) + '%';
        }

        // 填充数据表格
        function populateDataTable() {
            const tableBody = document.getElementById('dataTable');
            const metricNames = {
                'VmVportDropMetric/drop_ratio': '网络丢包率',
                'VmStealMetric/vmsteal': 'VM窃取时间',
                'VmStorageIOLatency/read_iops': '存储读取IOPS',
                'VmMemBWMetric/memory_bw': '内存带宽',
                'VmPpsBps/rx_pps': '网络接收PPS',
                'VmStorageIOLatency/write_lat_ms': '存储写延迟',
                'VmStorageIOLatency/read_lat_ms': '存储读延迟',
                'VmStorageIOLatency/write_iops': '存储写入IOPS',
                'VmPpsBps/tx_pps': '网络发送PPS'
            };

            Object.entries(performanceData).forEach(([key, data]) => {
                const stats = calculateStats(data);
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${metricNames[key] || key}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${stats.count}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatNumber(stats.min)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatNumber(stats.max)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatNumber(stats.avg)}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            updateOverviewCards();
            populateDataTable();
        });
    </script>
</body>
</html>