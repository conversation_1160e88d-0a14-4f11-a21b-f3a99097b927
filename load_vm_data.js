// 从JSON文件加载VM性能数据的脚本
async function loadVMPerformanceData() {
    try {
        // 这里需要替换为实际的JSON文件路径
        const jsonFilePath = 'src/vm_data_output/file_system/req_5360df80-6e2d-416a-abf6-9bdb939f99b5/vm_performance_eci-0xi16p1femf8dbmxdqpp_20250901_193622.json';
        
        // 在实际环境中，你需要通过服务器提供这个文件
        // 这里提供一个示例，展示如何处理真实数据
        
        const response = await fetch(jsonFilePath);
        const data = await response.json();
        
        return data;
    } catch (error) {
        console.error('加载数据失败:', error);
        // 返回示例数据作为后备
        return getSampleData();
    }
}

function getSampleData() {
    // 这是从你的JSON文件中提取的真实数据结构
    return {
        "VmVportDropMetric/drop_ratio": [
            {"timestamp": 1756312380, "datetime": "2025-08-28 00:33:00", "value": 0.0},
            {"timestamp": 1756312560, "datetime": "2025-08-28 00:36:00", "value": 0.0},
            {"timestamp": 1756312680, "datetime": "2025-08-28 00:38:00", "value": 0.0},
            {"timestamp": 1756312740, "datetime": "2025-08-28 00:39:00", "value": 0.0002907681377941676},
            {"timestamp": 1756312800, "datetime": "2025-08-28 00:40:00", "value": 0.002600927178146232},
            {"timestamp": 1756312920, "datetime": "2025-08-28 00:42:00", "value": 0.0},
            {"timestamp": 1756312980, "datetime": "2025-08-28 00:43:00", "value": 0.0},
            {"timestamp": 1756313040, "datetime": "2025-08-28 00:44:00", "value": 0.0},
            {"timestamp": 1756313100, "datetime": "2025-08-28 00:45:00", "value": 0.0033548910785121512},
            {"timestamp": 1756313160, "datetime": "2025-08-28 00:46:00", "value": 0.0}
        ],
        // 其他指标数据...
    };
}

// 数据处理工具函数
function processVMData(rawData) {
    const processedData = {};
    
    Object.entries(rawData).forEach(([metricName, dataPoints]) => {
        processedData[metricName] = {
            data: dataPoints,
            stats: calculateMetricStats(dataPoints),
            chartData: dataPoints.map(point => ({
                x: point.datetime,
                y: point.value
            }))
        };
    });
    
    return processedData;
}

function calculateMetricStats(dataPoints) {
    const values = dataPoints.map(d => d.value);
    const sortedValues = [...values].sort((a, b) => a - b);
    
    return {
        count: values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        median: sortedValues[Math.floor(sortedValues.length / 2)],
        p95: sortedValues[Math.floor(sortedValues.length * 0.95)],
        p99: sortedValues[Math.floor(sortedValues.length * 0.99)]
    };
}

// 导出函数供HTML使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadVMPerformanceData,
        processVMData,
        calculateMetricStats
    };
}