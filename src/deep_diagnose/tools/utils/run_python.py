
import asyncio
import json
from datetime import datetime
from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data


async def main():
    print("🚀 开始执行性能数据分析...")

    # 从任务描述中提取参数
    instance_id = "eci-0xi16p1femf8dbmxdqpp"
    start_time = "2025-08-28 00:00:00"
    end_time = "2025-08-28 05:00:00"
    metrics = [
        "VmStealMetric/vcpucpu",
        "VmStealMetric/vmsteal",
        "VmStorageIOLatency/read_lat_ms",
        "VmStorageIOLatency/write_lat_ms",
        "VmStorageIOLatency/read_iops",
        "VmStorageIOLatency/write_iops",
        "VmPpsBps/tx_pps",
        "VmPpsBps/rx_pps",
        "VmVportDropMetric/drop_ratio",
        "VmMemBWMetric/memory_bw"
    ]

    # 验证并获取request_id
    request_id = "req_6fbf56f8-8029-415c-bb45-a8647c12245a"
    if not request_id or request_id.strip() == "":
        request_id = "default_request"
        print("⚠️ request_id为空，使用默认值")

    print(f"📋 使用请求ID: {request_id}")

    # 验证request_id格式
    if not isinstance(request_id, str) or len(request_id) < 3:
        print("❌ request_id格式无效")
        return

    try:
        print(f"📊 正在获取实例 {instance_id} 的性能数据...")
        performance_data = await list_vm_performance_metrics(instance_id, metrics, start_time, end_time)

        if not performance_data:
            print("❌ 未获取到性能数据")
            return

        print("💾 正在保存分析结果...")
        save_result = save_and_register_data(request_id, instance_id, performance_data)

        if save_result["success"]:
            print(f"✅ 分析结果已保存: {save_result['json_file']}")
            print(f"📊 文件大小: {save_result['file_size']}")
            print(f"📈 指标数量: {save_result['metrics_count']}")
            print(f"📋 数据点数: {save_result['data_points']}")
        else:
            print(f"❌ 保存失败: {save_result['error']}")

    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


# 执行主函数
asyncio.run(main())
print("✅ 任务执行完成！")