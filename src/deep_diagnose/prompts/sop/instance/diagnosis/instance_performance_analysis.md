# SOP：实例性能数据采集与异常诊断

## 诊断步骤（必须按顺序执行）

### 步骤1：实例性能异常诊断
**目标**：分析实例性能异常，获取初步诊断结果
- **工具**：`runPerformanceDiagnose`
- **输入参数**：实例ID（用户提供的实例标识）,时间范围（建议为用户报告时间前6小时，时间范围不超过6小时）
- **分析要点**：
  - 识别性能异常类型（CPU、内存、IO、网络）
  - 确定异常时间范围
  - 评估异常严重程度
- **关键输出内容**：
  - 性能异常类型：CPU高使用率/内存不足/IO瓶颈/网络异常/其他
  - 异常时间段：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
  - 异常严重程度：轻微/中等/严重
  - 初步诊断结论：性能异常的可能原因

### 步骤2：查询实例对应的NC信息
**目标**：获取实例所在的物理机（NC）信息
- **工具**：`getVmBasicInfo`
- **输入参数**：实例ID
- **分析要点**：
  - 提取NC IP地址
  - 确认实例与NC的映射关系
  - 记录实例基本配置信息
- **关键输出内容**：
  - NC IP地址：xxx.xxx.xxx.xxx
  - 实例规格：CPU核数、内存大小、磁盘配置
  - 实例状态：运行中/已停止/其他
  - 实例创建时间：YYYY-MM-DD HH:MM:SS

### 步骤3：检查实例异常状态
**目标**：查询实例是否存在监控异常或告警
- **工具**：`listMonitorExceptions`
- **输入参数**：实例ID、时间范围（基于步骤1获取的异常时间段）
- **分析逻辑**：
  - 返回内容为空 → 监控层面无异常记录
  - 返回内容不为空 → 分析监控异常与性能问题的关联性
- **关键输出内容**：
  - 监控异常状态：有异常/无异常
  - 异常类型：CPU告警/内存告警/磁盘告警/网络告警/其他
  - 异常详情：具体的监控指标和阈值
  - 异常时间与性能问题关联：时间匹配度分析


### 步骤4：检查NC异常事件信息
**目标**：依赖步骤2的NC IP，检查步骤2的NC是否存在监控异常或告警
- **工具**：`listMonitorExceptions`
- **输入参数**：NC IP、时间范围（如果存在异常事件段，则异常时间段前后扩展（建议扩展2小时，总时间长度不超过6小时）。如果没有异常时间段，则采纳用户需求给出时间段）
- **分析逻辑**：
  - 返回内容为空 → 监控层面无异常记录
  - 返回内容不为空 → 分析监控异常与性能问题的关联性
- **关键输出内容**：
  - 监控异常状态：有异常/无异常
  - 异常类型：CPU告警/内存告警/磁盘告警/网络告警/其他
  - 异常时间与性能问题关联：时间匹配度分析
  - 
### 步骤5：采集实例的性能数据
**目标**：通过code agent采集详细的实例的性能数据
- **工具**：`coder` (code agent)
- **输入参数**：
  - 实例ID：来源用户问题中的实例ID
  - 性能指标：用户要求的指标信息
  - **关键性能指标选择**：
    - **CPU异常**：`["VmStealMetric/vcpucpu", "VmStealMetric/vmsteal"]`
    - **内存异常**：`["VmMemBWMetric/memory_bw"]`
    - **IO异常**：`["VmStorageIOLatency/read_lat_ms", "VmStorageIOLatency/write_lat_ms", "VmStorageIOLatency/read_iops", "VmStorageIOLatency/write_iops"]`
    - **网络异常**：`["VmPpsBps/tx_pps", "VmPpsBps/rx_pps", "VmVportDropMetric/drop_ratio"]`
  - 时间范围：用户需求给出时间范围
  - 数据用途：time_series（时间序列图表）
  - **关键输出内容**：
    - 数据采集状态：成功/失败
    - 生成文件路径：具体的JSON文件路径

