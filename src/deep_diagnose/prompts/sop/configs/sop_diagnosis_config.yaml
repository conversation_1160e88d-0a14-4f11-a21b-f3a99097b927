# 诊断SOP配置文件
# 包含SOP定义和关键词匹配规则

# SOP诊断配置
diagnosis_sops:
  - sop_id: "instance_batch_unavailable"
    name: "批量实例不可用诊断方案"
    scenario: |
      适用场景：适用于涉及多个实例（通常4个或以上）的批量不可用问题。
      识别规则：
      - 提到多个实例ID（4个或以上）
      - 描述'批量'、'多个'、'这些实例'等词汇
      - 实例在相同时间段出现相同问题
      - 大规模的实例可用性问题
    template_file_path: "instance/diagnosis/instance_unavailable_batch.md"
    example_user_queries:
      - "实例i-xxx、i-yyy、i-zzz、i-aaa同时不可用"
      - "批量实例在凌晨2点都出现了连接问题"
      - "这12个实例都无法访问"
      - "多个实例同时出现故障"
    tool_dependencies:
      - "listVmHostHistory"
      - "runVmUnavailableDiagnose"
      - "listChangeRecords"

  - sop_id: "instance_single_restart"
    name: "单实例重启或宕机诊断"
    scenario: |
      适用场景：适用于单个实例或少量实例（通常1-3个）的重启、宕机问题诊断。
      识别规则：
      - 单个或少量实例异常重启
      - 实例突然宕机或无响应
      - 实例自动重启原因不明
      - 非批量性的实例可用性问题
    template_file_path: "instance/diagnosis/instance_restart_single.md"
    example_user_queries:
      - "实例i-xxx异常重启了"
      - "服务器突然宕机"
      - "实例自动重启，不知道什么原因"
      - "单个实例无响应"
    tool_dependencies:
      - "getVmBasicInfo"
      - "listMonitorExceptions"
      - "listOperationRecords"
      - "listChangeRecords"
      - "listActionTrail"
      - "listReportedOperationalEvents"
      - "get_vm_coredump"
      - "runDiagnose"

  - sop_id: "nc_host_crash_analysis"
    name: "物理机宕机根因诊断"
    scenario: |
      适用场景：适用于明确是物理机（NC/CN）发生宕机，需要通过分析vmcore日志来定位宕机根因的场景。
      识别规则：
      - 需要进行vmcore分析
      - 物理机宕机根因分析
      - NC/CN宕机诊断
      - 必须包含宕机、down 等关键词语
    template_file_path: "nc/diagnosis/nc_host_crash_analysis.md"
    example_user_queries:
      - "基于vmcore分析NC(***********)的宕机根因"
      - "通过内核、虚拟化、金轮分析物理机111.111.11的宕机根因"
      - "NC宕机vmcore诊断"
    tool_dependencies:
      - "getNcDownRecord"
      - "analyzeVmcoreFromCore"
      - "analyzeVmcoreFromVirt"
      - "analyzeVmcoreFromJinlun"
      - "listVmsOnNc"
      - "listChangeRecords"
      - "getNcDownLog"

  - sop_id: "customer_stability_check"
    name: "产出客户报告信息"
    scenario: |
      适用场景：适用于需要为客户生成健康度检查报告的场景，主要针对客户VM实例的整体健康状况进行评估。
      识别规则：
      - 查询用户健康报告
      - 客户VM健康度检查
      - 用户实例稳定性评估
      - 客户环境健康度分析
    template_file_path: "customer/diagnosis/customer_stability_report.md"
    example_user_queries:
      - "查询用户aliUid 1781574661016173的最近2天健康报告"
    tool_dependencies:
      - "checkHealthForCustomerVms"

  - sop_id: "instance_performance_analysis"
    name: "实例性能数据采集与异常诊断"
    scenario: |
      适用场景：适用于实例性能异常问题的深度诊断，包括CPU、内存、IO、网络等性能指标的分析。
      识别规则：
      - 实例性能异常或性能问题
      - 需要采集性能数据进行分析
      - CPU使用率高、内存不足、IO瓶颈、网络异常等
      - 需要生成性能图表和数据分析
      - 包含'性能'、'CPU'、'内存'、'IO'、'网络'、'延迟'、'吞吐量'等关键词
    template_file_path: "instance/diagnosis/instance_performance_analysis.md"
    example_user_queries:
      - "实例i-xxx性能异常，需要分析CPU和内存使用情况"
      - "服务器响应慢，帮忙分析性能数据"
      - "实例IO延迟很高，需要采集性能指标"
      - "网络吞吐量异常，分析网络性能"
      - "采集实例性能数据并生成图表"
      - "分析实例的CPU使用率趋势"
    tool_dependencies:
      - "runPerformanceDiagnose"
      - "getVmBasicInfo"
      - "listMonitorExceptions"

  - sop_id: "general_troubleshooting"
    scenario: |
      适用场景：适用于各类常见问题的通用诊断流程，作为其他专项SOP的补充。
      识别规则：
      - 网络连接相关问题
      - 配置相关问题
      - 其他未分类的通用问题
    name: "其他问题"
    template_file_path: "instance/diagnosis/default.md"
    example_user_queries:
      - "网络连接问题"
      - "配置问题"
      - "实例规格"
    tool_dependencies: []