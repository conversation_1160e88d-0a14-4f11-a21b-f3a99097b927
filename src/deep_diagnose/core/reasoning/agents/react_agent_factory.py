"""
Agent工具函数 - 纯粹的公共工具，不包含业务逻辑
"""

import json
from dataclasses import dataclass
from enum import Enum
from typing import List, Any
from langchain_core.messages import ToolMessage, BaseMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.common.config.constants.workflow import DEFAULT_MAX_RECURSIVE_NUM
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.core.reasoning.workflow.types import ReasoningState

from deep_diagnose.core.reasoning.planning import create_sop_service



@dataclass
class ToolAnalysis:
    """工具执行结果分析"""
    is_tool: bool
    success: bool
    empty: bool
    text: str


class ToolResultAnalyzer:
    """ToolMessage 结果分析器 - 独立处理工具执行结果"""
    
    @staticmethod
    def is_truly_empty(obj: Any) -> bool:
        """更精确的空值判断：null、空字符串、空数组、空字典"""
        if obj is None:
            return True
        if isinstance(obj, str):
            return len(obj.strip()) == 0
        if isinstance(obj, (list, tuple, set)):
            return len(obj) == 0
        if isinstance(obj, dict):
            return len(obj) == 0
        # 对于数字类型，0 是有效值，不认为是空
        if isinstance(obj, (int, float)):
            return False
        # 对于布尔类型，False 是有效值，不认为是空
        if isinstance(obj, bool):
            return False
        # 对于其他类型，只有在转换为字符串后确实为空字符串时才认为是空
        try:
            str_val = str(obj).strip()
            return len(str_val) == 0 or str_val.lower() in ('none', 'null')
        except Exception:
            return False

    @staticmethod
    def parse_raw_content(raw: str) -> tuple[str, bool]:
        """解析原始内容，只返回内容和是否为空"""
        try:
            parsed = json.loads(raw) if raw else None
        except Exception:
            parsed = None
            
        if parsed is None:
            text = raw or ""
            empty = len(text.strip()) == 0
            return text, empty
            
        # 检查解析后的内容是否为空
        content_empty = ToolResultAnalyzer.is_truly_empty(parsed)
        
        # 直接使用整个解析对象，不再查找特定字段
        try:
            text = parsed if isinstance(parsed, str) else json.dumps(parsed, ensure_ascii=False)
        except Exception:
            text = str(parsed)
            
        return text, content_empty

    @staticmethod
    def analyze_tool_message(msg: ToolMessage) -> ToolAnalysis:
        """分析单个 ToolMessage - 优先使用 ToolMessage.status 判断成功"""
        # 1. 优先检查 ToolMessage.status 属性
        msg_status = msg.status
        final_success = False
        
        if msg_status is not None:
            # 如果有 status 属性，优先使用它判断成功
            status_success = str(msg_status).lower() == "success"
            
            # 解析内容
            raw = str(msg.content) if msg.content is not None else ""
            text, content_empty = ToolResultAnalyzer.parse_raw_content(raw)
            
            # 使用 ToolMessage.status 作为最终成功判断
            final_success = status_success
            
            return ToolAnalysis(True, final_success, content_empty, text)
        
        # 2. 如果没有 status 属性，回退到内容分析
        raw = str(msg.content) if msg.content is not None else ""
        text, empty = ToolResultAnalyzer.parse_raw_content(raw)
        return ToolAnalysis(True, final_success, empty, text)

    @staticmethod
    def analyze_last_message(messages: List[BaseMessage]) -> ToolAnalysis:
        """分析消息列表中的最后一个消息"""
        if not messages:
            return ToolAnalysis(False, False, True, "")
        last = messages[-1]
        if not isinstance(last, ToolMessage):
            return ToolAnalysis(False, False, True, "")
        return ToolResultAnalyzer.analyze_tool_message(last)

    @staticmethod
    def count_consecutive_failures(messages: List[BaseMessage]) -> int:
        """统计最后一个ToolMessage的工具名称的连续失败次数"""
        if not messages:
            return 0
            
        # 首先找到最后一个ToolMessage，获取其工具名称
        target_tool_name = None
        for m in reversed(messages):
            if isinstance(m, ToolMessage):
                target_tool_name = getattr(m, 'name', 'unknown')
                break
        
        if target_tool_name is None:
            return 0
        
        # 然后统计该工具名称的连续失败次数
        count = 0
        for m in reversed(messages):
            if isinstance(m, ToolMessage):
                tool_name = getattr(m, 'name', 'unknown')
                
                # 只统计目标工具的消息
                if tool_name == target_tool_name:
                    analysis = ToolResultAnalyzer.analyze_tool_message(m)
                    if analysis.success:
                        break
                    else:
                        count += 1
                # 如果遇到其他工具的消息，跳过继续
            # 非ToolMessage也跳过继续
            
        return count

    @staticmethod
    def count_consecutive_failures_any_tool(messages: List[BaseMessage]) -> int:
        """统计任意工具的连续失败次数 - 不区分工具类型"""
        count = 0
        
        for m in reversed(messages):
            if isinstance(m, ToolMessage):
                analysis = ToolResultAnalyzer.analyze_tool_message(m)
                
                if analysis.success:
                    break
                else:
                    count += 1
            else:
                continue
            
        return count


# ==================== 原有功能函数 ====================

async def setup_mcp_tools(agent_type: str, config: RunnableConfig) -> List:
    """为指定agent设置MCP工具 - 纯工具函数"""
    try:
        mcp_tool_manager = MCPToolManager()
        # 使用新的MCP服务获取过滤后的工具
        filtered_tools = await mcp_tool_manager.get_enabled_mcp_tools()
        return filtered_tools
    except Exception:
        return []


class Phase(str, Enum):
    """代理执行阶段枚举"""
    SELECT = "select"
    FINAL = "final"


@dataclass
class PhaseDecision:
    """阶段决策结果"""
    phase: Phase
    last_is_tool_message: bool = False
    last_tool_success: bool = False
    consecutive_tool_failures: int = 0
    tool_result: str = ""
    tool_result_empty: bool = True


class PhaseDetector:
    """阶段检测器 - 独立处理阶段检测逻辑"""
    
    @staticmethod
    def detect_phase(agent_state: dict[str, Any]) -> PhaseDecision:
        """检测当前应该执行的阶段"""
        msgs = agent_state.get("messages") or []
        last = msgs[-1] if msgs else None

        # 无消息时选择工具
        if not msgs:
            return PhaseDecision(Phase.SELECT)
        
        # 最后一条不是工具消息时选择工具
        if not isinstance(last, ToolMessage):
            return PhaseDecision(Phase.SELECT)
        
        # 分析工具执行结果
        analysis = ToolResultAnalyzer.analyze_last_message(msgs)

        if not analysis.is_tool:
            return PhaseDecision(Phase.SELECT)
        
        # 工具执行成功，进入最终阶段
        if analysis.success:
            return PhaseDecision(
                Phase.FINAL,
                last_is_tool_message=True,
                last_tool_success=True,
                consecutive_tool_failures=0,
                tool_result=analysis.text,
                tool_result_empty=analysis.empty
            )
        
        # 工具执行失败，使用混合策略统计失败次数
        same_tool_failures = ToolResultAnalyzer.count_consecutive_failures(msgs)

        # 混合策略：同一工具失败3次 OR 任意工具失败5次
        should_enter_final = same_tool_failures >= 3
        final_failure_count =same_tool_failures
        
        if should_enter_final:
            return PhaseDecision(Phase.FINAL,
                                 last_is_tool_message=True,
                                 last_tool_success=False,
                                 consecutive_tool_failures=final_failure_count,
                                 tool_result=analysis.text,
                                 tool_result_empty=analysis.empty)
        else:
            return PhaseDecision(
                Phase.SELECT,
                last_is_tool_message=True,
                last_tool_success=False,
                consecutive_tool_failures=final_failure_count,
                tool_result=analysis.text,
                tool_result_empty=analysis.empty
            )


async def create_agent_with_tools(
    agent_type: str, 
    tools: List, 
    config: RunnableConfig,
    step_title: str = None,
    step_description: str = None,
    request_id: str = None
):
    """创建带工具的agent - 支持并发工具调用，按阶段切换提示词
    
    Args:
        agent_type: 智能体类型
        tools: 工具列表
        config: 运行配置
        step_title: 当前执行步骤的标题
        step_description: 当前执行步骤的详细描述
    """

    configurable = Configuration.from_runnable_config(config)
    mcp_tool_manager = MCPToolManager()

    # Initialize SOPService for this factory
    sop_service = create_sop_service()

    async def generate_prompt(agent_state):
        """生成提示词 - 根据阶段检测结果选择合适的模板"""
        # 根据SOP获取工具列表过滤描述（仅在工具选择阶段注入，避免答复阶段过度干扰）
        sop_name = agent_state.get("sop_name", "")
        tool_names = None
        if sop_name:
            sop_obj = await sop_service.store.get_sop_by_id(sop_name)
            if sop_obj:
                tool_names = sop_obj.tool_dependencies

        # 使用新的阶段检测器
        decision = PhaseDetector.detect_phase(agent_state)

        # 创建新的状态对象，保留原始状态中的所有数据
        current_state = ReasoningState()
        # 清空messages，因为这是agent内部的消息，不需要传递给模板
        current_state["request_id"] = request_id
        current_state["messages"] = []

        if decision.phase == Phase.SELECT:
            # 工具选择阶段：注入工具描述
            current_state["mcp_servers_description"] = await mcp_tool_manager.get_enabled_mcp_tools_description(tool_names)
            prompt_name = f"agent_{agent_type}_select"
        else:
            # 最终回答阶段：注入工具执行结果
            current_state["tool_result"] = decision.tool_result
            current_state["tool_result_empty"] = decision.tool_result_empty
            prompt_name = f"agent_{agent_type}_final"
        
        if tool_names:
            pass

        # 注入步骤信息（通过参数显式传递，确保可靠性）
        if step_title is not None:
            current_state["step_title"] = step_title
        if step_description is not None:
            current_state["step_description"] = step_description
        
        # 在应用模板时注入 configurable（之前未使用）
        return apply_prompt_template(prompt_name, current_state, configurable)

    # 获取 LLM 并启用并发工具调用
    llm = get_llm_by_type(AGENT_LLM_MAP[agent_type])

    # 确保 LLM 支持并发工具调用
    if hasattr(llm, 'parallel_tool_calls'):
        llm.parallel_tool_calls = True
    elif hasattr(llm, 'bind'):
        # 使用 bind 方法启用并发工具调用
        llm = llm.bind(parallel_tool_calls=True)

    return create_react_agent(
        name=agent_type,
        model=llm,
        tools=tools,
        prompt=generate_prompt,
    )


def get_recursion_limit() -> int:
    """获取递归限制配置 - 纯工具函数"""
    default_limit = DEFAULT_MAX_RECURSIVE_NUM
    return default_limit